import { useNavigate } from "react-router-dom";
import { useUserContext } from "../context/UserContext";
import { useState } from "react";

export default function LoginPage() {
  const { login, isLoading, error } = useUserContext();
  const navigate = useNavigate();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    const result = await login(email, password);
    if (result) {
      navigate("/");
    }
  }

  return (
    <main className="w-full h-[100dvh] flex flex-col gap-4 items-center justify-center">
      <h1 className="text-3xl font-bold">Adeptos admin</h1>

      <form onSubmit={onSubmit} className="flex gap-2">
        <div className="flex flex-col gap-2">
          <input value={email} onChange={e => setEmail(e.target.value)} placeholder="Email" className="bg-gray-200 rounded p-2" />
          <input value={password} onChange={e => setPassword(e.target.value)} type="password" placeholder="Password" className="w-full bg-gray-200 rounded p-2" />
        </div>
        <button
          disabled={isLoading}
          type="submit"
          className="w-[120px] bg-blue-500 text-white rounded p-2"
        >{isLoading ? "Loading..." : "Login"}</button>
      </form>
      {error && <p>{error}</p>}
    </main>
  );
}
