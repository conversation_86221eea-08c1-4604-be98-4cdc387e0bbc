import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { Dashboard } from './pages/Dashboard.tsx';
import ProtectedRoute from './components/ProtectedRoute.tsx';
import LoginPage from './pages/Login.tsx';
import { UserProvider } from './context/UserContext.tsx';
import { BusinessProvider } from './context/BusinessContext.tsx';

const router = createBrowserRouter([
  {
    element: <ProtectedRoute />,
    children: [
      { index: true, element: <Dashboard /> },
    ],
  },
  { path: "/login", element: <LoginPage /> },
]);

export default function App() {
  return (
    <UserProvider>
      <BusinessProvider>
        <RouterProvider router={router} />
      </BusinessProvider>
    </UserProvider>
  )
}