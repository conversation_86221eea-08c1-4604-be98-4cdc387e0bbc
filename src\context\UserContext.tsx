import { createContext, useContext, useState, useEffect, type ReactNode, useMemo } from "react";
import { getFrom<PERSON>pi, postToApi } from "../utils/api";

interface UserContextType {
  user: string | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  fetchUser: () => void
  isLoading: boolean
  error: string | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider = ({ children }: UserProviderProps) => {
  const [user, setUser] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUser();
  }, []);

  const fetchUser = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getFrom<PERSON>pi('/auth/me');
      if (response.error) {
        setUser(null);
        setError(response.error);
        return;
      }
      setUser(response.data);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);
    const response = await postToApi('/auth/login', {
      email,
      password,
    });
    if (response.error) {
      setError(response.error);
      setIsLoading(false);
      return false;
    }
    setUser(response.data);
    setIsLoading(false);
    return true;
  };

  const logout = () => {
    alert('Logout (not implemented)');
  }

  const value = useMemo<UserContextType>(() => ({
    user,
    login,
    logout,
    fetchUser,
    isLoading,
    error,
  }), [user, isLoading, error]);

  return <UserContext.Provider value={value}> {children} </UserContext.Provider>;
}

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
}