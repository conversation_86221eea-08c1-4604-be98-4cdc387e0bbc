export function Tabs({
  value,
  onChange,
}: {
  value: "all" | "paid" | "unpaid";
  onChange: (v: "all" | "paid" | "unpaid") => void;
}) {
  const base =
    "inline-flex items-center rounded-xl px-3 py-2 text-sm font-medium ring-1 ring-slate-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500";
  const active = "bg-slate-800 text-white";
  const inactive = "bg-slate-900 text-slate-300 hover:bg-slate-800";
  return (
    <div className="inline-flex gap-2 rounded-2xl bg-slate-900 p-1 ring-1 ring-slate-800">
      <button
        aria-current={value === "all"}
        onClick={() => onChange("all")}
        className={`${base} ${value === "all" ? active : inactive}`}
      >
        All
      </button>
      <button
        aria-current={value === "paid"}
        onClick={() => onChange("paid")}
        className={`${base} ${value === "paid" ? active : inactive}`}
      >
        Paid
      </button>
      <button
        aria-current={value === "unpaid"}
        onClick={() => onChange("unpaid")}
        className={`${base} ${value === "unpaid" ? active : inactive}`}
      >
        Unpaid
      </button>
    </div>
  );
}