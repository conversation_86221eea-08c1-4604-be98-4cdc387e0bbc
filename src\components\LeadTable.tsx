import type { Lead } from '../types/lead';
import type { Transaction } from '../types/transaction';
import { Th } from './ui/Th';
import { Td } from './ui/Td';

interface LeadTableProps {
  leads: Lead[];
  transactions: Transaction[];
}

function groupBy<T, K>(list: T[], getKey: (item: T) => K): Map<K, T[]> {
  const map = new Map<K, T[]>();
  for (const item of list) {
    const key = getKey(item);
    const arr = map.get(key);
    if (arr) arr.push(item);
    else map.set(key, [item]);
  }
  return map;
}
export function LeadTable({ leads, transactions }: LeadTableProps) {
  const txByLead = groupBy(transactions, (t) => t.leadId);

  if (!leads?.length) {
    return (
      <h2>No leads to show</h2>
    );
  }

  return (
    <div className="overflow-hidden rounded-xl ring-1 ring-slate-800">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-800">
          <thead className="bg-slate-900 sticky top-0 z-10">
            <tr>
              <Th>Id</Th>
              <Th className="pl-4">Name</Th>
              <Th>Email</Th>
              <Th>Company</Th>
              <Th>Status</Th>
              <Th className="pr-4 text-right">Amount</Th>
            </tr>
          </thead>
          <tbody className="divide-y divide-slate-800 bg-slate-950/40">
            {leads.map((lead) => {
              const leadTx = txByLead.get(lead.id) ?? [];
              const isPaid = leadTx.length > 0;
              const amount = leadTx.reduce((acc, t) => acc + (t.amount || 0), 0);

              return (
                <tr
                  key={lead.id}
                  className="hover:bg-slate-900/60 transition-colors"
                >
                  <Td>
                    <span className="font-medium">{lead.id}</span>
                  </Td>
                  <Td className="pl-4">
                    <div className="flex flex-col">
                      <span className="font-medium">{lead.fullName}</span>
                    </div>
                  </Td>
                  <Td>
                    <a
                      href={`mailto:${lead.email}`}
                      className="text-indigo-400 hover:text-indigo-300"
                    >
                      {lead.email}
                    </a>
                  </Td>
                  <Td>{lead.companyName || "-"}</Td>
                  <Td>
                    <StatusBadge paid={isPaid} />
                  </Td>
                  <Td className="pr-4 text-right">
                    {isPaid ? `$${(amount / 100).toFixed(2)}` : '-'}
                  </Td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function StatusBadge({ paid }: { paid: boolean }) {
  if (paid) {
    return (
      <span className="inline-flex items-center gap-1 rounded-full bg-emerald-500/15 px-2 py-1 text-xs font-medium text-emerald-300 ring-1 ring-emerald-500/30">
        <span className="h-2 w-2 rounded-full bg-emerald-400" />
        Paid
      </span>
    );
  }
  return (
    <span className="inline-flex items-center gap-1 rounded-full bg-rose-500/15 px-2 py-1 text-xs font-medium text-rose-300 ring-1 ring-rose-500/30">
      <span className="h-2 w-2 rounded-full bg-rose-400" />
      Unpaid
    </span>
  );
}