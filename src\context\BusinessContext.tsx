import { createContext, use<PERSON>ontext, useMemo, useState, type <PERSON>actNode } from "react";
import type { Lead } from "../types/lead";
import type { Transaction } from "../types/transaction";
import { getFromApi } from "../utils/api";

type IsLoading = {
  leads: boolean;
  transactions: boolean;
}

interface BusinessContextType {
  entities: {
    leads: Lead[];
    transactions: Transaction[];
  };
  loading: IsLoading
  error: string | null;
  fetchActions: {
    fetchLeads: () => void;
    fetchTransactions: () => void;
  }
}

const BusinessContext = createContext<BusinessContextType | undefined>(undefined);

interface BusinessProviderProps {
  children: ReactNode;
}

export const BusinessProvider = ({ children }: BusinessProviderProps) => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState<IsLoading>({
    leads: false,
    transactions: false,
  });
  const [error, setError] = useState<string | null>(null);

  const fetchLeads = async () => {
    setLoading({
      ...loading,
      leads: true,
    });
    setError(null);
    const response = await getFromApi('/leads');
    if (response.error) {
      setError(response.error);
      setLoading({
        ...loading,
        leads: false,
      });
      return;
    }
    setLeads(response.data);
    setLoading({
      ...loading,
      leads: false,
    });
    console.log("Leads", response.data);
  }

  const fetchTransactions = async () => {
    setLoading({
      ...loading,
      transactions: true,
    });
    setError(null);
    const response = await getFromApi('/transactions');
    if (response.error) {
      setError(response.error);
      setLoading({
        ...loading,
        transactions: false,
      });
      return;
    }
    setTransactions(response.data);
    setLoading({
      ...loading,
      transactions: false,
    });
    console.log("Transactions", response.data);
  }

  const value = useMemo<BusinessContextType>(() => ({
    entities: {
      leads,
      transactions,
    },
    loading,
    error,
    fetchActions: {
      fetchLeads,
      fetchTransactions,
    },
  }), [leads, transactions, loading, error]);

  return <BusinessContext.Provider value={value}> {children} </BusinessContext.Provider>;
}

export const useBusinessContext = () => {
  const context = useContext(BusinessContext);
  if (!context) {
    throw new Error('useBusinessContext must be used within a BusinessProvider');
  }
  return context;
} 