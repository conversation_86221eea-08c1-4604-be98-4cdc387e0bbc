import type { Report } from './report';

export interface Lead {
  id: number;
  fullName: string;
  companyName: string;
  email: string;
  phone: string;
  website: string;
  businessType: string;
  employeeCount: string;
  industry: string;
  monthlyRevenue: string;
  salesLocation: string;
  bottlenecks: string;
  timeConsumingTasks: string;
  reports: Report[];
  createdAt: string;
  updatedAt: string;
}