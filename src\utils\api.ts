const apiBase = import.meta.env.VITE_API_BASE
type ApiResponse = {
  data: any;
  error: string | null;
}

export const getFromApi = async (url: string): Promise<ApiResponse> => {
  try {
    const response = await fetch(`${apiBase}${url}`, {
      credentials: 'include',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const backResponse: ApiResponse = await response.json()
    if (!response.ok) {
      return {
        data: null,
        error: backResponse.error,
      }
    }

    return {
      data: backResponse.data,
      error: null,
    }

  } catch (error) {
    console.error(error)
    return {
      data: null,
      error: 'Something went wrong',
    }
  }
}

export const postToApi = async (url: string, data: any): Promise<ApiResponse> => {
  try {
    const response = await fetch(`${apiBase}${url}`, {
      credentials: 'include',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const backResponse: ApiResponse = await response.json()
    if (!response.ok) {
      return {
        data: null,
        error: backResponse.error,
      }
    }
    return {
      data: backResponse.data,
      error: null,
    }
  } catch (error) {
    console.error(error)
    return {
      data: null,
      error: 'Something went wrong',
    }
  }
}