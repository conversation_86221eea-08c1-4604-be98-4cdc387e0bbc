import { useEffect, useMemo, useState } from 'react';
import { useBusinessContext } from '../context/BusinessContext';
import { LeadTable } from '../components/LeadTable';
import { StatCard } from '../components/StatCard';
import { Tabs } from '../components/Tabs';
import { Search } from 'lucide-react';

type TabKey = "all" | "paid" | "unpaid";
export function Dashboard() {
  const [selected, setSelected] = useState<TabKey>("all");
  const [q, setQ] = useState("");
  const {
    entities: {
      leads,
      transactions
    },
    fetchActions: {
      fetchLeads,
      fetchTransactions
    },
    loading: {
      leads: leadsLoading,
      transactions: transactionsLoading
    },
  } = useBusinessContext();

  useEffect(() => {
    fetchLeads();
    fetchTransactions();
  }, []);

  const paidLeadIds = useMemo(
    () => new Set(transactions.map((t) => t.leadId)),
    [transactions]
  );

  const paidLeads = useMemo(
    () => leads.filter((l) => paidLeadIds.has(l.id)),
    [leads, paidLeadIds]
  );
  const unpaidLeads = useMemo(
    () => leads.filter((l) => !paidLeadIds.has(l.id)),
    [leads, paidLeadIds]
  );

  const leadsMap: Record<TabKey, typeof leads> = {
    all: leads,
    paid: paidLeads,
    unpaid: unpaidLeads,
  };

  const leadsToShow = useMemo(() => {
    const base = leadsMap[selected];
    if (!q.trim()) return base;
    const term = q.toLowerCase();
    return base.filter(
      (l) =>
        l.fullName.toLowerCase().includes(term) ||
        l.email.toLowerCase().includes(term) ||
        l.companyName.toLowerCase().includes(term)
    );
  }, [leadsMap, selected, q]);

  const totalRevenue = useMemo(
    () => transactions.reduce((acc, t) => acc + (t.amount || 0), 0),
    [transactions]
  );

  if (leadsLoading || transactionsLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-slate-950 text-slate-100">
      <div className="mx-auto max-w-7xl px-6 py-8">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">Dashboard</h1>
            <p className="text-sm text-slate-400">
              General vision of leads and payments
            </p>
          </div>
        </div>

        {/* Métricas */}
        <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            label="Total leads"
            value={leads.length}
            hint="Created"
          />
          <StatCard label="Paid leads" value={paidLeads.length} hint="With payment" />
          <StatCard
            label="Unpaid leads"
            value={unpaidLeads.length}
            hint="Pending"
          />
          <StatCard
            label="Total revenue"
            value={totalRevenue}
            hint="Accumulated"
          />
        </div>

        {/* Controles */}
        <div className="mb-4 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <Tabs value={selected} onChange={setSelected} />
          <div className="relative">
            <input
              value={q}
              onChange={(e) => setQ(e.target.value)}
              placeholder="Search by name, email or company..."
              className="w-full rounded-xl border-0 bg-slate-900 px-4 py-2.5 text-sm text-slate-100 ring-1 ring-slate-700 placeholder:text-slate-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:w-80"
            />
            <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-slate-500">
              <Search></Search>
            </span>
          </div>
        </div>

        {/* Tabla */}
        <div className="rounded-2xl bg-slate-900/60 p-4 ring-1 ring-slate-800">
          <LeadTable leads={leadsToShow} transactions={transactions} />
        </div>
      </div>
    </div>
  );
}
